import { FormEventHandler, useState } from "react";
import "@/styles/Login.css";
import { Head, useForm } from "@inertiajs/react";

type LoginForm = {
  email: string;
  password: string;
  remember: boolean;
};

interface LoginProps {
  status?: string;
  canResetPassword: boolean;
}

function Login({ status, canResetPassword }: LoginProps) {
  const { data, setData, post, processing, errors, reset } = useForm<Required<LoginForm>>({
    email: '',
    password: '',
    remember: false,
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();
    post(route('login'), {
      onFinish: () => reset('password'),
    });
  };

  return (
    <div className="login-container">
      <Head title="Log in" />

      <div className="login-columns">
        <div className="login-left-column">
          <div className="login-form-container">
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/ff79270abae4069c92179445e6e8601c1e5c3f7c?placeholderIfAbsent=true"
              alt="Logo"
              className="login-logo"
            />
            <div className="login-title">Sign in to continue to</div>
            <div className="login-brand">SmartRX</div>
            <form onSubmit={submit}>
              <div className="login-input-group">
                <label className="login-label" htmlFor="email">Email</label>
                <input
                  id="email"
                  type="email"
                  className="login-input"
                  placeholder="<EMAIL>"
                  value={data.email}
                  onChange={(e) => setData('email', e.target.value)}
                  required
                />
                {errors.email && <div className="text-red-500 text-sm mt-1">{errors.email}</div>}

                <label className="login-label mt-3" htmlFor="password">Password</label>
                <input
                  id="password"
                  type="password"
                  className="login-input"
                  placeholder="Please enter your password"
                  value={data.password}
                  onChange={(e) => setData('password', e.target.value)}
                  required
                />
                {errors.password && <div className="text-red-500 text-sm mt-1">{errors.password}</div>}

                <div className="hidden">
                  <div className="flex justify-between items-center gap-3 mt-3">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="remember"
                        checked={data.remember}
                        onChange={() => setData('remember', !data.remember)}
                      />
                      <label htmlFor="remember" className="login-label">Remember me</label>
                    </div>

                    {canResetPassword && (
                      <div className="mt-2">
                        <a href={route('password.request')} className="text-sm text-blue-600">
                          Forgot password?
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <button type="submit" className="login-button" disabled={processing}>
                <div className="login-button-content">
                  <div className="login-button-text">Continue</div>
                  {!processing && (
                    <img
                      src="https://cdn.builder.io/api/v1/image/assets/TEMP/8613d7282780742ffc65a186f820afbcdc8c00c2?placeholderIfAbsent=true"
                      alt="Continue"
                      className="login-button-icon"
                    />
                  )}
                  {processing && <span className="animate-spin">↻</span>}
                </div>
              </button>
            </form>
            <div className="login-divider">
              <img
                src="https://cdn.builder.io/api/v1/image/assets/TEMP/8212cb9bbc5357f8e3a76b4a01945b11a6775e30?placeholderIfAbsent=true"
                alt="Divider"
                className="login-divider-line"
              />
              <div>OR</div>
              <img
                src="https://cdn.builder.io/api/v1/image/assets/TEMP/4cedcae492264e9ec8205de02c0d88c68d1c2746?placeholderIfAbsent=true"
                alt="Divider"
                className="login-divider-line"
              />
            </div>
            <button type="button" className="login-sso-button">Pfizer SSO</button>
            <div className="login-footer">
              Don't have an account?{" "}
              <a href={route('register')} className="login-footer-link">
                Sign up
              </a>
            </div>

            {status && <div className="mt-4 text-center text-sm font-medium text-green-600">{status}</div>}
          </div>
        </div>
        <div className="login-right-column">
          <div className="login-hero">
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/dcf80d9b74e5872fd282ae4c1988529aa0b1e5f1?placeholderIfAbsent=true"
              alt="Background"
              className="login-hero-bg"
            />
            <div className="login-hero-overlay">
              <div className="login-testimonial">
                <div className="login-testimonial-content">
                  <div className="login-testimonial-text">
                    <img
                      src="https://cdn.builder.io/api/v1/image/assets/TEMP/f140a092255034d37727d8d5d5ecd438e2fb6dd0?placeholderIfAbsent=true"
                      alt="Quote"
                      className="login-testimonial-icon"
                    />
                    <div className="login-testimonial-quote">
                      Access real-time analytics, campaign insights, and
                      AI-powered recommendations — all in one secure platform
                      tailored for your team. Pfizer SmartXR is now available
                      for leadership, marketing and media teams!
                    </div>
                    <div className="login-testimonial-author">
                      <div className="login-author-name">Kozhin Fatah</div>
                      <div className="login-author-title">
                        Technical Delivery Manager, OSS
                      </div>
                    </div>
                  </div>
                  <div className="login-testimonial-image">
                    <img
                      src="https://cdn.builder.io/api/v1/image/assets/TEMP/d33ebb1730532211dedbc8ba796a9d96d6a8dc77?placeholderIfAbsent=true"
                      alt="Testimonial"
                      className="login-testimonial-photo"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Login;