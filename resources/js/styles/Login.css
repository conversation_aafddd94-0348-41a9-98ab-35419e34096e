.login-container {
  background-color: rgba(250, 250, 250, 1);
  box-shadow: 0px 3px 6px rgba(18, 15, 40, 0.12);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.login-columns {
  display: flex;
  flex: 1;
}

.login-left-column {
  display: flex;
  flex-direction: column;
  width: 41%;
  min-height: 100vh;
}

.login-form-container {
  border-radius: 6px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 80px 60px;
  font-family: "Noto Sans", -apple-system, Roboto, Helvetica, sans-serif;
  font-size: 18px;
  line-height: 1.5;
}

.login-logo {
  aspect-ratio: 2.42;
  object-fit: contain;
  object-position: center;
  width: 116px;
  max-width: 100%;
}

.login-title {
  color: rgba(26, 26, 26, 1);
  font-size: 40px;
  font-weight: 400;
  line-height: 1.4;
  margin-top: 41px;
  margin-right: 29px;
}

.login-brand {
  color: rgba(0, 0, 103, 1);
  font-size: 40px;
  font-weight: 400;
  line-height: 1.4;
  align-self: flex-start;
  margin-top: 6px;
}

.login-input-group {
  background-color: transparent;
  display: flex;
  margin-top: 35px;
  padding: 1px 0;
  flex-direction: column;
  align-items: stretch;
  white-space: nowrap;
}

.login-label {
  color: rgba(74, 74, 74, 1);
  font-weight: 700;
  align-self: flex-start;
}

.login-input {
  border-radius: 8px;
  background-color: rgba(244, 244, 244, 1);
  border: 1px solid transparent;
  padding: 12px 20px;
  color: #333;
  font-weight: 400;
  width: 100%;
  transition: border-color 0.3s;
}

.login-input:focus {
  border-color: rgba(0, 0, 103, 1);
  outline: none;
}

.login-input::placeholder {
  color: rgba(193, 193, 193, 1);
}

.login-button {
  border-radius: 8px;
  background-color: rgba(0, 0, 103, 1);
  border: 1px solid transparent;
  display: flex;
  margin-top: 16px;
  width: 100%;
  padding: 12px 70px;
  flex-direction: column;
  overflow: hidden;
  align-items: center;
  color: #fff;
  font-weight: 400;
  white-space: nowrap;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

.login-button:hover {
  background-color: rgba(0, 0, 130, 1);
}

.login-button-content {
  display: flex;
  width: 105px;
  max-width: 100%;
  align-items: stretch;
  gap: 4px;
}

.login-button-text {
  flex-grow: 1;
}

.login-button-icon {
  aspect-ratio: 1;
  object-fit: contain;
  object-position: center;
  width: 24px;
  margin: auto 0;
  flex-shrink: 0;
}

.login-divider {
  display: flex;
  margin-top: 24px;
  justify-content: center;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: rgba(95, 95, 95, 1);
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
  line-height: 2;
}

.login-divider-line {
  aspect-ratio: 200;
  object-fit: contain;
  object-position: center;
  width: 188px;
  align-self: stretch;
  margin: auto 0;
  flex-shrink: 0;
  max-width: 100%;
}

.login-sso-button {
  border-radius: 8px;
  background-color: rgba(28, 115, 231, 1);
  border: 1px solid transparent;
  margin-top: 24px;
  padding: 12px 70px;
  overflow: hidden;
  color: #fff;
  font-weight: 400;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

.login-sso-button:hover {
  background-color: rgba(40, 125, 240, 1);
}

.login-footer {
  color: rgba(26, 26, 26, 1);
  font-size: 14px;
  font-weight: 400;
  line-height: 2;
  text-align: center;
  align-self: center;
  margin-top: 164px;
  margin-left: 11px;
}

.login-footer-link {
  color: rgba(0, 0, 103, 1);
  text-decoration: none;
}

.login-right-column {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  line-height: normal;
  width: 59%;
  margin-left: 20px;
}

.login-hero {
  display: flex;
  flex-direction: column;
  position: relative;
  min-height: 900px;
  flex-grow: 1;
}

.login-hero-bg {
  position: absolute;
  inset: 0;
  height: 100%;
  width: 100%;
  object-fit: cover;
  object-position: center;
}

.login-hero-overlay {
  position: relative;
  background-color: rgba(0, 0, 103, 0.9);
  display: flex;
  width: 100%;
  padding: 310px 70px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.login-testimonial {
  border-radius: 12px;
  background-color: #fff;
  box-shadow: 0px 0px 2px rgba(26, 26, 26, 0.2);
  margin-bottom: -62px;
  width: 603px;
  max-width: 100%;
  padding: 20px 21px;
}

.login-testimonial-content {
  display: flex;
  gap: 20px;
}

.login-testimonial-text {
  width: 68%;
}

.login-testimonial-icon {
  aspect-ratio: 1;
  object-fit: contain;
  object-position: center;
  width: 28px;
}

.login-testimonial-quote {
  color: rgba(56, 56, 56, 1);
  font-size: 18px;
  font-weight: 400;
  line-height: 28px;
  margin-left: 28px;
}

.login-testimonial-author {
  align-self: center;
  display: flex;
  margin-top: -9px;
  width: 344px;
  max-width: 100%;
  align-items: stretch;
  font-size: 16px;
  line-height: 2;
}

.login-author-name {
  color: rgba(26, 26, 26, 1);
  font-weight: 700;
}

.login-author-title {
  color: rgba(120, 120, 120, 1);
  font-weight: 400;
  flex-grow: 1;
  flex-shrink: 1;
  width: 222px;
}

.login-testimonial-image {
  width: 32%;
  margin-left: 20px;
}

.login-testimonial-photo {
  aspect-ratio: 0.79;
  object-fit: contain;
  object-position: center;
  width: 188px;
  margin-top: 22px;
  flex-shrink: 0;
  max-width: 100%;
  flex-grow: 1;
}

@media (max-width: 991px) {
  .login-columns {
    flex-direction: column;
    align-items: stretch;
    gap: 0;
  }

  .login-left-column,
  .login-right-column {
    width: 100%;
    margin-left: 0;
  }

  .login-form-container {
    max-width: 100%;
    padding: 100px 20px;
  }

  .login-title {
    margin-right: 10px;
    margin-top: 40px;
  }

  .login-button,
  .login-sso-button {
    padding-left: 20px;
    padding-right: 20px;
  }

  .login-footer {
    margin-top: 40px;
  }

  .login-hero {
    max-width: 100%;
  }

  .login-hero-overlay {
    padding: 100px 20px;
  }

  .login-testimonial {
    margin-bottom: 10px;
    padding-right: 20px;
  }

  .login-testimonial-content {
    flex-direction: column;
    align-items: stretch;
    gap: 0;
  }

  .login-testimonial-text {
    margin-top: 20px;
    margin-right: -18px;
  }

  .login-testimonial-quote {
    margin-left: 10px;
  }

  .login-author-name {
    margin-right: -3px;
  }

  .login-testimonial-photo {
    margin-top: 40px;
  }
}

